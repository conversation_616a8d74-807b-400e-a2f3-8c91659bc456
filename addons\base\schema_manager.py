"""
Base Module Schema Manager
Handles database schema operations for the base module installation.
Separated from installer for clear separation of concerns.
"""
from typing import Dict, List, Optional, TYPE_CHECKING
from erp.logging import get_logger

if TYPE_CHECKING:
    from erp.database.manager import DatabaseManager


class BaseSchemaManager:
    """
    Manages database schema operations for base module installation.
    Handles table creation, constraints, and indexes specifically for base models.
    """

    # Base module specific constants
    BASE_MODELS = [
        'ir.module.module', # IrModuleModule
        'ir.model', # IrModel
        'ir.model.fields' # IrModelFields
    ]

    BASE_MODEL_MODULE_MAP = {
        'ir.module.module': 'addons.base.models.ir_module',
        'ir.model': 'addons.base.models.ir_model',
        'ir.model.fields': 'addons.base.models.ir_model',  # IrModelFields is in ir_model.py
    }

    BASE_MODEL_CLASS_MAP = {
        'ir.module.module': 'IrModuleModule',
        'ir.model': 'IrModel',
        'ir.model.fields': 'IrModelFields',
    }

    BASE_CONSTRAINTS = [
        {
            'table': 'ir_module_module',
            'constraint_name': 'uk_ir_module_module_name',
            'sql': 'ALTER TABLE ir_module_module ADD CONSTRAINT uk_ir_module_module_name UNIQUE (name)'
        },
        {
            'table': 'ir_model',
            'constraint_name': 'uk_ir_model_name',
            'sql': 'ALTER TABLE ir_model ADD CONSTRAINT uk_ir_model_name UNIQUE (model)'
        },
        {
            'table': 'ir_model_fields',
            'constraint_name': 'uk_ir_model_fields_model_name',
            'sql': 'ALTER TABLE ir_model_fields ADD CONSTRAINT uk_ir_model_fields_model_name UNIQUE (model, name)'
        }
    ]

    BASE_INDEXES = [
        """CREATE INDEX IF NOT EXISTS idx_ir_model_fields_model
           ON ir_model_fields (model)""",
        """CREATE INDEX IF NOT EXISTS idx_ir_model_fields_name
           ON ir_model_fields (name)""",
        """CREATE INDEX IF NOT EXISTS idx_ir_module_module_state
           ON ir_module_module (state)""",
        """CREATE INDEX IF NOT EXISTS idx_ir_model_state
           ON ir_model (state)""",
    ]

    def __init__(self):
        self.logger = get_logger(__name__)

    async def import_base_models(self) -> bool:
        """Import base models to ensure they are available for schema generation"""
        try:
            # Import the base models
            from addons.base.models import ir_module, ir_model
            self.logger.debug("✓ Base models imported successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to import base models: {e}")
            return False

    async def generate_base_tables(self, db_manager: 'DatabaseManager') -> bool:
        """Generate database tables for base models"""
        try:
            from erp.utils.schema import SchemaGenerator

            self.logger.debug("Generating base model tables...")

            # Use the generic method with base-specific parameters
            table_results = await SchemaGenerator.generate_model_tables(
                db_manager,
                self.BASE_MODELS,
                self.BASE_MODEL_MODULE_MAP,
                self.BASE_MODEL_CLASS_MAP
            )

            failed_tables = [model for model, success in table_results.items() if not success]
            if failed_tables:
                self.logger.error(f"Failed to create tables for models: {', '.join(failed_tables)}")
                return False

            self.logger.debug("✓ All base model tables generated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to generate base tables: {e}")
            return False

    async def setup_base_constraints_and_indexes(self, db_manager: 'DatabaseManager') -> bool:
        """Set up base-specific constraints and indexes"""
        try:
            from erp.utils.schema import SchemaGenerator

            # Use the generic method with base-specific constraints and indexes
            success = await SchemaGenerator.setup_table_constraints(
                db_manager,
                self.BASE_CONSTRAINTS,
                self.BASE_INDEXES
            )

            if not success:
                self.logger.error("Failed to set up base constraints and indexes")
                return False

            self.logger.debug("✓ Base constraints and indexes set up successfully")
            return True

        except Exception as e:
            self.logger.error(f"✗ Error setting up base constraints: {e}")
            return False

    async def validate_base_tables(self, db_manager: 'DatabaseManager') -> bool:
        """Validate that all base tables exist and have correct structure"""
        try:
            for model_name in self.BASE_MODELS:
                table_name = model_name.replace('.', '_')

                # Check if table exists
                exists = await db_manager.fetchval(
                    """SELECT EXISTS (
                       SELECT FROM information_schema.tables
                       WHERE table_schema = 'public'
                       AND table_name = $1
                    )""",
                    table_name
                )

                if not exists:
                    self.logger.error(f"Core table {table_name} does not exist after installation")
                    return False

            self.logger.debug("✓ All core tables validated successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to validate base tables: {e}")
            return False

    async def create_base_schema(self, db_manager: 'DatabaseManager') -> bool:
        """
        Complete base schema creation process.
        This is the main entry point for schema operations.
        """
        try:
            # Step 1: Import base models
            if not await self.import_base_models():
                return False

            # Step 2: Generate base model tables
            if not await self.generate_base_tables(db_manager):
                return False

            # Step 3: Set up constraints and indexes
            if not await self.setup_base_constraints_and_indexes(db_manager):
                return False

            # Step 4: Validate tables
            if not await self.validate_base_tables(db_manager):
                return False

            self.logger.debug("✓ Base schema creation completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create base schema: {e}")
            return False
